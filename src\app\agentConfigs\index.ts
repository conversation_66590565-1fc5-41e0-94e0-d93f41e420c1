import { simpleHandoffScenario } from "./simpleHandoff";
import { customerServiceRetailScenario } from "./customerServiceRetail";
import { chatSupervisorScenario } from "./chatSupervisor";
import { Agent } from "@openai/agents";

// Map of scenario key -> array of BasicAgent objects
export const allAgentSets: Record<string, Agent[]> = {
  simpleHandoff: simpleHandoffScenario,
  customerServiceRetail: customerServiceRetailScenario,
  chatSupervisor: chatSupervisorScenario,
};

export const defaultAgentSetKey = "chatSupervisor";
