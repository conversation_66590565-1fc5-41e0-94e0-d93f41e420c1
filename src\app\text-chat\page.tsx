"use client";
import React, { useState, useCallback } from "react";
import { TranscriptProvider } from "@/app/contexts/TranscriptContext";
import { EventProvider } from "@/app/contexts/EventContext";
import TextAgentChat from "@/app/components/TextAgentChat";
import { allAgentSets, defaultAgentSetKey } from "@/app/agentConfigs";

export default function TextChatPage() {
  const [selectedAgentSetKey, setSelectedAgentSetKey] = useState(defaultAgentSetKey);
  const [apiKey, setApiKey] = useState("");

  const getApiKey = useCallback(async () => {
    if (!apiKey) {
      throw new Error("Please enter your OpenAI API key");
    }
    return apiKey;
  }, [apiKey]);

  const handleAgentSetChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedAgentSetKey(e.target.value);
  };

  const selectedAgents = allAgentSets[selectedAgentSetKey] || [];

  return (
    <TranscriptProvider>
      <EventProvider>
        <div className="min-h-screen bg-gray-100">
          <div className="container mx-auto py-8">
            <div className="mb-6">
              <h1 className="text-3xl font-bold mb-4">Text-Based Agent Chat</h1>
              <p className="text-gray-600 mb-4">
                This demo shows how to use the OpenAI Agents SDK with text-based interactions.
                It preserves all the functionality of the realtime version including tool calls,
                handoffs, and history logging, but works with text instead of audio.
              </p>
              
              {/* Configuration */}
              <div className="bg-white rounded-lg p-6 mb-6">
                <h2 className="text-xl font-semibold mb-4">Configuration</h2>
                
                {/* API Key Input */}
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-2">
                    OpenAI API Key
                  </label>
                  <input
                    type="password"
                    value={apiKey}
                    onChange={(e) => setApiKey(e.target.value)}
                    placeholder="sk-..."
                    className="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Your API key is only used locally and never sent to any server except OpenAI.
                  </p>
                </div>

                {/* Agent Set Selection */}
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-2">
                    Agent Scenario
                  </label>
                  <select
                    value={selectedAgentSetKey}
                    onChange={handleAgentSetChange}
                    className="w-full p-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {Object.keys(allAgentSets).map((key) => (
                      <option key={key} value={key}>
                        {key}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Agent Info */}
                <div className="mb-4">
                  <h3 className="text-sm font-medium mb-2">Available Agents:</h3>
                  <div className="space-y-2">
                    {selectedAgents.map((agent, index) => (
                      <div key={index} className="p-3 bg-gray-50 rounded">
                        <div className="font-medium">{agent.name}</div>
                        <div className="text-sm text-gray-600">
                          {agent.handoffDescription || "No description available"}
                        </div>
                        {agent.handoffs && agent.handoffs.length > 0 && (
                          <div className="text-xs text-blue-600 mt-1">
                            Can hand off to: {agent.handoffs.map(h => h.name).join(", ")}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Chat Interface */}
            {apiKey && selectedAgents.length > 0 ? (
              <div className="bg-white rounded-lg shadow-lg h-96">
                <TextAgentChat
                  agents={selectedAgents}
                  getApiKey={getApiKey}
                />
              </div>
            ) : (
              <div className="bg-white rounded-lg p-8 text-center">
                <p className="text-gray-500">
                  Please enter your API key and select an agent scenario to start chatting.
                </p>
              </div>
            )}

            {/* Features Info */}
            <div className="mt-8 bg-white rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Features Preserved</h2>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="p-4 bg-blue-50 rounded">
                  <h3 className="font-medium text-blue-800 mb-2">🔧 Tool Calls</h3>
                  <p className="text-sm text-blue-600">
                    Agents can still use tools and the calls are logged in the history.
                  </p>
                </div>
                <div className="p-4 bg-green-50 rounded">
                  <h3 className="font-medium text-green-800 mb-2">🔄 Handoffs</h3>
                  <p className="text-sm text-green-600">
                    Agents can hand off conversations to other agents seamlessly.
                  </p>
                </div>
                <div className="p-4 bg-purple-50 rounded">
                  <h3 className="font-medium text-purple-800 mb-2">📝 History Logging</h3>
                  <p className="text-sm text-purple-600">
                    All events are logged and can be viewed in the transcript.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </EventProvider>
    </TranscriptProvider>
  );
}
