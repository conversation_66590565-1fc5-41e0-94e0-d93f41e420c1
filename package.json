{"name": "realtime-examples", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@openai/agents": "^0.0.9", "@radix-ui/react-icons": "^1.3.2", "dotenv": "^16.4.7", "next": "^15.3.1", "openai": "^4.77.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^9.0.3", "uuid": "^11.0.4", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}