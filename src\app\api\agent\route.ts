import { NextRequest, NextResponse } from "next/server";
import { allAgentSets } from "@/app/agentConfigs";
import { run } from "@openai/agents";

export async function POST(req: NextRequest) {
  try {
    const { agentSetKey, agentName, userText } = await req.json();
    const agents = allAgentSets[agentSetKey];
    if (!agents) {
      return NextResponse.json({ error: "Invalid agent set" }, { status: 400 });
    }
    const agent = agents.find((a) => a.name === agentName);
    if (!agent) {
      return NextResponse.json({ error: "Invalid agent" }, { status: 400 });
    }
    const agentReply = await run(agent, userText);
    // Try to extract handoff events if present (ignore TS error if not typed)
    let handoffs = [];
    const anyReply = agentReply as any;
    if (anyReply.generatedItems) {
      handoffs = anyReply.generatedItems
        .filter((item: any) => item.type === "handoff_output_item")
        .map((item: any) => ({
          from: item.sourceAgent?.name,
          to: item.targetAgent?.name,
        }));
    }
    return NextResponse.json({ reply: agentReply.finalOutput, handoffs });
  } catch (err: any) {
    return NextResponse.json(
      { error: err.message || "Agent error" },
      { status: 500 }
    );
  }
}
