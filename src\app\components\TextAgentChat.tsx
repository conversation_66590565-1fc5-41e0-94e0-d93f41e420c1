"use client";
import React, { useState, useCallback } from "react";
import { Agent } from "@openai/agents";
import { useTextAgentSession } from "../hooks/useTextAgentSession";

interface TextAgentChatProps {
  agents: Agent[];
  getApiKey: () => Promise<string>;
}

export default function TextAgentChat({ agents, getApiKey }: TextAgentChatProps) {
  const [userInput, setUserInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [currentAgentName, setCurrentAgentName] = useState("");

  const handleConnectionChange = useCallback((status: string) => {
    console.log("Connection status:", status);
  }, []);

  const handleAgentHandoff = useCallback((agentName: string) => {
    console.log("Agent handoff to:", agentName);
    setCurrentAgentName(agentName);
  }, []);

  const { status, connect, disconnect, sendUserText, conversationHistory } = useTextAgentSession({
    onConnectionChange: handleConnectionChange,
    onAgentHandoff: handleAgentHandoff,
  });

  const handleConnect = useCallback(async () => {
    try {
      await connect({
        getApiKey,
        initialAgents: agents,
        extraContext: {},
      });
      if (agents.length > 0) {
        setCurrentAgentName(agents[0].name);
      }
    } catch (error) {
      console.error("Failed to connect:", error);
    }
  }, [connect, getApiKey, agents]);

  const handleDisconnect = useCallback(() => {
    disconnect();
    setCurrentAgentName("");
  }, [disconnect]);

  const handleSendMessage = useCallback(async () => {
    if (!userInput.trim() || isLoading) return;

    setIsLoading(true);
    try {
      await sendUserText(userInput.trim());
      setUserInput("");
    } catch (error) {
      console.error("Failed to send message:", error);
    } finally {
      setIsLoading(false);
    }
  }, [userInput, sendUserText, isLoading]);

  const handleKeyPress = useCallback((e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  }, [handleSendMessage]);

  return (
    <div className="flex flex-col h-full max-w-4xl mx-auto p-4">
      <div className="mb-4">
        <h2 className="text-xl font-semibold mb-2">Text Agent Chat</h2>
        <div className="flex items-center gap-4 mb-2">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Status:</span>
            <span className={`px-2 py-1 rounded text-xs font-medium ${
              status === "CONNECTED" 
                ? "bg-green-100 text-green-800" 
                : status === "CONNECTING"
                ? "bg-yellow-100 text-yellow-800"
                : "bg-red-100 text-red-800"
            }`}>
              {status}
            </span>
          </div>
          {currentAgentName && (
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Current Agent:</span>
              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">
                {currentAgentName}
              </span>
            </div>
          )}
        </div>
        <div className="flex gap-2">
          {status === "DISCONNECTED" ? (
            <button
              onClick={handleConnect}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              Connect
            </button>
          ) : (
            <button
              onClick={handleDisconnect}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            >
              Disconnect
            </button>
          )}
        </div>
      </div>

      {/* Conversation History */}
      <div className="flex-1 border rounded-lg p-4 mb-4 overflow-y-auto bg-white">
        <h3 className="font-medium mb-2">Conversation History</h3>
        {conversationHistory.length === 0 ? (
          <p className="text-gray-500 italic">No messages yet...</p>
        ) : (
          <div className="space-y-2">
            {conversationHistory.map((item, index) => {
              // Handle different types of AgentInputItem
              const isUserMessage = 'role' in item && item.role === "user";
              const isAssistantMessage = 'role' in item && item.role === "assistant";
              const role = 'role' in item ? item.role : 'system';
              const content = 'content' in item ? item.content : JSON.stringify(item);

              return (
                <div
                  key={index}
                  className={`p-3 rounded-lg ${
                    isUserMessage
                      ? "bg-blue-50 border-l-4 border-blue-400"
                      : isAssistantMessage
                      ? "bg-green-50 border-l-4 border-green-400"
                      : "bg-gray-50 border-l-4 border-gray-400"
                  }`}
                >
                  <div className="font-medium text-sm mb-1 capitalize">
                    {role}
                  </div>
                  <div className="text-sm">
                    {typeof content === "string"
                      ? content
                      : JSON.stringify(content)
                    }
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Input Area */}
      <div className="border rounded-lg p-4 bg-white">
        <div className="flex gap-2">
          <textarea
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message here..."
            className="flex-1 p-2 border rounded resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={3}
            disabled={status !== "CONNECTED" || isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={status !== "CONNECTED" || isLoading || !userInput.trim()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? "Sending..." : "Send"}
          </button>
        </div>
        <div className="text-xs text-gray-500 mt-2">
          Press Enter to send, Shift+Enter for new line
        </div>
      </div>
    </div>
  );
}
