# Text-Based Agent Implementation

This document explains how to use the adapted hooks for text-based OpenAI Agents without realtime session and audio, while preserving handoff, tools, and history event logging functionality.

## Overview

The text-based implementation provides the same core functionality as the realtime version but works with text input/output instead of audio. All the important features are preserved:

- ✅ **Tool calls** - Agents can use tools and calls are logged
- ✅ **Agent handoffs** - Seamless transfer between agents
- ✅ **History logging** - All events are captured and logged
- ✅ **Event tracking** - Client and server events are tracked
- ✅ **Session management** - Connect/disconnect lifecycle

## Key Files

### 1. `useTextAgentSession.ts`
The main hook that replaces `useRealtimeSession` for text-based interactions.

**Key differences from realtime version:**
- Uses `run()` function from OpenAI Agents SDK instead of RealtimeSession
- No audio-related functionality (mute, pushToTalk, etc.)
- Simpler connection process (no WebRTC)
- Text-based message sending with `sendUserText()`

### 2. `useHandleSessionHistory.ts` (Enhanced)
Enhanced the existing session history handler to include:
- Added `handleAgentHandoff()` function for logging agent transfers
- All existing functionality preserved (tool calls, history updates, etc.)

### 3. `TextAgentChat.tsx`
Example React component demonstrating how to use the text agent session.

### 4. `text-chat/page.tsx`
Complete demo page showing the text-based agent chat in action.

## Usage

### Basic Setup

```typescript
import { useTextAgentSession } from "../hooks/useTextAgentSession";
import { Agent } from "@openai/agents";

const agents = [/* your agents */];

const { status, connect, disconnect, sendUserText, conversationHistory } = useTextAgentSession({
  onConnectionChange: (status) => console.log("Status:", status),
  onAgentHandoff: (agentName) => console.log("Handoff to:", agentName),
});
```

### Connecting

```typescript
await connect({
  getApiKey: async () => "your-openai-api-key",
  initialAgents: agents,
  extraContext: {}, // optional context
});
```

### Sending Messages

```typescript
const result = await sendUserText("Hello, how can you help me?");
console.log("Agent response:", result.finalOutput);
```

### Disconnecting

```typescript
disconnect();
```

## API Reference

### `useTextAgentSession(callbacks?)`

**Parameters:**
- `callbacks` (optional): Object with callback functions
  - `onConnectionChange?: (status: SessionStatus) => void`
  - `onAgentHandoff?: (agentName: string) => void`

**Returns:**
- `status`: Current connection status ("DISCONNECTED" | "CONNECTING" | "CONNECTED")
- `connect(options)`: Function to establish connection
- `disconnect()`: Function to close connection
- `sendUserText(text)`: Function to send text message to agent
- `conversationHistory`: Array of conversation history items

### `TextConnectOptions`

```typescript
interface TextConnectOptions {
  getApiKey: () => Promise<string>;
  initialAgents: Agent[];
  extraContext?: Record<string, any>;
}
```

## Event Logging

The text agent session automatically logs the following events:

### Client Events
- Connection status changes
- User message sending

### Server Events
- Agent responses
- Tool call starts/ends
- Agent handoffs
- Errors

### History Events
- Message additions (user and assistant)
- Tool call executions
- Agent transfers

## Agent Handoffs

Agent handoffs work the same way as in the realtime version:

1. Agent decides to transfer conversation
2. `onHandoff` callback is triggered in the run options
3. `handleAgentHandoff` logs the transfer
4. `onAgentHandoff` callback notifies the UI
5. Current agent reference is updated

## Tool Calls

Tool calls are handled through the OpenAI Agents SDK:

1. Agent decides to use a tool
2. `onToolStart` callback logs the tool call
3. Tool executes
4. `onToolEnd` callback logs the result
5. Agent continues with the tool result

## Error Handling

Errors are automatically logged and can be handled in your UI:

```typescript
try {
  await sendUserText("Hello");
} catch (error) {
  console.error("Failed to send message:", error);
  // Handle error in UI
}
```

## Migration from Realtime

To migrate from the realtime version:

1. Replace `useRealtimeSession` with `useTextAgentSession`
2. Replace `RealtimeAgent` with `Agent` in your agent configurations
3. Remove audio-related code (mute, pushToTalk, etc.)
4. Replace `sendUserText()` calls (API is the same)
5. Update connection options (remove audio elements, use regular API key)

## Example Agent Configuration

```typescript
import { Agent } from "@openai/agents";

export const greeterAgent = new Agent({
  name: "greeter",
  instructions: "Greet users and help them get started.",
  model: "gpt-4o-mini",
  handoffs: [/* other agents */],
  tools: [/* tool definitions */],
  handoffDescription: "Agent that greets users",
});
```

## Demo

Visit `/text-chat` in your application to see a working demo of the text-based agent chat.

## Benefits

1. **Simpler deployment** - No WebRTC or audio handling required
2. **Lower latency** - Direct API calls without realtime streaming
3. **Easier testing** - Text-based interactions are easier to test
4. **Same functionality** - All core agent features preserved
5. **Better logging** - Text conversations are easier to log and debug

## Limitations

- No voice/audio capabilities
- No real-time streaming (though this could be added)
- Requires manual message sending (no voice activation)

## Next Steps

The text-based implementation provides a solid foundation that could be extended with:

- Streaming responses for real-time text updates
- Rich text formatting and markdown support
- File upload capabilities
- Integration with other text-based channels (Slack, Discord, etc.)
