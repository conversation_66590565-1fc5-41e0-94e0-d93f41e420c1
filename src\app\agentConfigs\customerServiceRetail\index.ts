import { authenticationAgent } from "./authentication";
import { returnsAgent } from "./returns";
import { salesAgent } from "./sales";
import { simulatedHumanAgent } from "./simulatedHuman";

// Cast to `any` to satisfy TypeScript until the core types make RealtimeAgent
// assignable to `Agent<unknown>` (current library versions are invariant on
// the context type).
(authenticationAgent.handoffs as any).push(
  returnsAgent,
  salesAgent,
  simulatedHumanAgent
);
(returnsAgent.handoffs as any).push(
  authenticationAgent,
  salesAgent,
  simulatedHumanAgent
);
(salesAgent.handoffs as any).push(
  authenticationAgent,
  returnsAgent,
  simulatedHumanAgent
);
(simulatedHumanAgent.handoffs as any).push(
  authenticationAgent,
  returnsAgent,
  salesAgent
);

export const customerServiceRetailScenario = [
  authenticationAgent,
  returnsAgent,
  salesAgent,
  simulatedHumanAgent,
];

// Name of the company represented by this agent set. Used by guardrails
export const customerServiceRetailCompanyName = "Snowy Peak Boards";
