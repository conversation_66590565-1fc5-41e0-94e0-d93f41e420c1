/**
 * Utility functions for handling audio codecs in WebRTC connections
 */

export type AudioFormat = {
  type: "pcm16";
  sample_rate: number;
};

export type CodecParam = string;

/**
 * Get audio format configuration for a given codec
 */
export function audioFormatForCodec(codecParam: CodecParam): AudioFormat {
  // Default to 24kHz for high quality, 8kHz for narrow-band codecs
  const sampleRate = codecParam?.toLowerCase().includes('narrow') || 
                     codecParam?.toLowerCase().includes('8k') ? 8000 : 24000;
  
  return {
    type: "pcm16",
    sample_rate: sampleRate,
  };
}

/**
 * Apply codec preferences to a WebRTC peer connection
 */
export function applyCodecPreferences(
  pc: RTCPeerConnection, 
  codecParam: CodecParam
): RTCPeerConnection {
  if (!codecParam) return pc;

  try {
    // Get transceivers for audio
    const transceivers = pc.getTransceivers();
    const audioTransceiver = transceivers.find(t => 
      t.receiver?.track?.kind === 'audio' || 
      t.sender?.track?.kind === 'audio'
    );

    if (audioTransceiver) {
      // Get available codecs
      const capabilities = RTCRtpReceiver.getCapabilities('audio');
      if (capabilities?.codecs) {
        // Filter codecs based on the codec parameter
        let preferredCodecs = capabilities.codecs;
        
        if (codecParam.toLowerCase().includes('opus')) {
          preferredCodecs = capabilities.codecs.filter(codec => 
            codec.mimeType.toLowerCase().includes('opus')
          );
        } else if (codecParam.toLowerCase().includes('pcmu') || codecParam.toLowerCase().includes('g711')) {
          preferredCodecs = capabilities.codecs.filter(codec => 
            codec.mimeType.toLowerCase().includes('pcmu') ||
            codec.mimeType.toLowerCase().includes('g711')
          );
        }

        // Apply codec preferences if we found matching codecs
        if (preferredCodecs.length > 0) {
          audioTransceiver.setCodecPreferences(preferredCodecs);
        }
      }
    }
  } catch (error) {
    console.warn('Failed to apply codec preferences:', error);
  }

  return pc;
}
