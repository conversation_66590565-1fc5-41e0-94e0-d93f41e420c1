import { Agent } from "@openai/agents";

export const haikuWriterAgent = new Agent({
  name: "haikuWriter",
  instructions:
    "Ask the user for a topic, then reply with a haiku about that topic.",
  model: "gpt-4.1-nano",
  handoffs: [],
  tools: [],
  handoffDescription: "Agent that writes haikus",
});

export const greeterAgent = new Agent({
  name: "greeter",
  instructions:
    "Please greet the user and ask them if they'd like a Haiku. If yes, hand off to the 'haiku' agent.",
  model: "gpt-4.1-nano",
  handoffs: [haikuWriterAgent],
  tools: [],
  handoffDescription: "Agent that greets the user",
});

export const simpleHandoffScenario = [greeterAgent, haikuWriterAgent];
