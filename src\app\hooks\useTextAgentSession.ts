import { useCallback, useRef, useState } from "react";
import { Agent, run, AgentInputItem } from "@openai/agents";

import { useEvent } from "../contexts/EventContext";
import { useHandleSessionHistory } from "./useHandleSessionHistory";
import { SessionStatus } from "../types";

export interface TextAgentSessionCallbacks {
  onConnectionChange?: (status: SessionStatus) => void;
  onAgentHandoff?: (agentName: string) => void;
}

export interface TextConnectOptions {
  getApiKey: () => Promise<string>;
  initialAgents: Agent[];
  extraContext?: Record<string, any>;
}

/**
 * Hook for using text-based OpenAI Agents (no realtime/audio).
 * Provides similar interface to useRealtimeSession but for text-based interactions.
 * Handles tool calls, handoffs, and logs history events.
 */
export function useTextAgentSession(callbacks: TextAgentSessionCallbacks = {}) {
  const currentAgentRef = useRef<Agent | null>(null);
  const conversationHistoryRef = useRef<AgentInputItem[]>([]);
  const [status, setStatus] = useState<SessionStatus>("DISCONNECTED");
  const { logClientEvent, logServerEvent } = useEvent();

  const updateStatus = useCallback(
    (s: SessionStatus) => {
      setStatus(s);
      callbacks.onConnectionChange?.(s);
      logClientEvent({}, s);
    },
    [callbacks, logClientEvent]
  );

  const historyHandlers = useHandleSessionHistory().current;

  const handleAgentHandoff = useCallback(
    (agentName: string) => {
      callbacks.onAgentHandoff?.(agentName);
    },
    [callbacks]
  );

  const connect = useCallback(
    async ({
      getApiKey,
      initialAgents,
      extraContext,
    }: TextConnectOptions) => {
      if (currentAgentRef.current) return; // already connected

      updateStatus("CONNECTING");

      try {
        const apiKey = await getApiKey();
        
        // Set the OpenAI API key for the agents SDK
        if (typeof window === "undefined") {
          process.env.OPENAI_API_KEY = apiKey;
        }
        
        currentAgentRef.current = initialAgents[0];
        conversationHistoryRef.current = [];
        
        updateStatus("CONNECTED");
      } catch (error) {
        logServerEvent({
          type: "error",
          message: error instanceof Error ? error.message : "Unknown error",
        });
        updateStatus("DISCONNECTED");
        throw error;
      }
    },
    [updateStatus, logServerEvent]
  );

  const disconnect = useCallback(() => {
    currentAgentRef.current = null;
    conversationHistoryRef.current = [];
    updateStatus("DISCONNECTED");
  }, [updateStatus]);

  const assertConnected = () => {
    if (!currentAgentRef.current) {
      throw new Error("TextAgentSession not connected");
    }
  };

  const sendUserText = useCallback(
    async (text: string) => {
      assertConnected();
      
      // Log the user message
      const userItemId = `user-${Date.now()}`;
      historyHandlers.handleHistoryAdded({
        itemId: userItemId,
        type: "message",
        role: "user",
        content: [{ type: "input_text", text }]
      });

      try {
        // Create the new conversation history with the user message
        const newHistory = [...conversationHistoryRef.current, { role: "user", content: text }];
        
        // Use the simple run function
        const result = await run(
          currentAgentRef.current!,
          newHistory,
          {
            stream: false,
            onToolStart: (session: any, agent: Agent, toolCall: any) => {
              historyHandlers.handleAgentToolStart(session, agent, toolCall);
            },
            onToolEnd: (session: any, agent: Agent, toolCall: any, result: any) => {
              historyHandlers.handleAgentToolEnd(session, agent, toolCall, result);
            },
            onHandoff: (session: any, agent: Agent, handoff: any) => {
              historyHandlers.handleAgentHandoff(session, agent, handoff);
              handleAgentHandoff(handoff.agent || handoff.name);
            },
          }
        );

        // Update conversation history
        conversationHistoryRef.current = result.history;
        
        // Log the assistant response
        if (result.finalOutput) {
          historyHandlers.handleHistoryAdded({
            itemId: `assistant-${Date.now()}`,
            type: "message",
            role: "assistant",
            content: [{ type: "text", text: result.finalOutput }]
          });
        }

        // Check for handoffs in the result
        const handoffPattern = /transfer_to_(\w+)/;
        const handoffMatch = result.finalOutput?.match(handoffPattern);
        if (handoffMatch) {
          const agentName = handoffMatch[1];
          handleAgentHandoff(agentName);
        }
        
        return result;
      } catch (error) {
        logServerEvent({
          type: "error",
          message: error instanceof Error ? error.message : "Unknown error",
        });
        throw error;
      }
    },
    [historyHandlers, logServerEvent, handleAgentHandoff]
  );

  return {
    status,
    connect,
    disconnect,
    sendUserText,
    conversationHistory: conversationHistoryRef.current,
  } as const;
}
