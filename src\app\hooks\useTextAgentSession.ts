import { Agent, run } from "@openai/agents";
import { useHandleSessionHistory } from "./useHandleSessionHistory";

/**
 * Hook for using a text-only OpenAI Agent (no realtime/audio).
 * Handles tool calls, handoffs, and logs history events.
 */
export function useTextAgentSession(agent: Agent) {
  const historyHandlers = useHandleSessionHistory().current;

  /**
   * Send a user message to the agent and handle tool/handoff/history events.
   * @param text User's message
   * @param history Conversation history (array of items)
   */
  async function sendUserMessage(text: string, history: any[]) {
    const response = await run(agent, text, {
      context: { history },
      onToolStart: historyHandlers.handleAgentToolStart,
      onToolEnd: historyHandlers.handleAgentToolEnd,
      onHandoff: historyHandlers.handleAgentHandoff,
    });
    historyHandlers.handleHistoryAdded(response);
    return response;
  }

  return { sendUserMessage };
}
